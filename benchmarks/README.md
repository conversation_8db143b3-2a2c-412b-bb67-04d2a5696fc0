# rr<PERSON><PERSON> vs n<PERSON>hmann/json Benchmark

A comprehensive performance comparison between **rrjson** and **nlohmann/json**, the most popular C++ JSON library.

## 🎯 What This Benchmark Tests

### Core Performance Metrics
- **Parsing Speed**: How fast each library can parse JSON data
- **Access Performance**: Speed of reading values from parsed JSON
- **Modification Capabilities**: JSON data modification support and speed
- **Serialization**: Converting JSON back to string format
- **Error Handling**: Exception handling performance and robustness
- **Memory Usage**: Memory footprint comparison

### Test Scenarios
1. **Small API Response** (100 objects) - Typical REST API responses
2. **Medium Data Export** (1000 objects) - Data export scenarios
3. **Large Bulk Data** (10000 objects) - Big data processing
4. **Deeply Nested Config** (500 objects, 5 levels) - Configuration files

## 🚀 Quick Start

### Option 1: Automated Setup
```bash
# Install dependencies and run benchmark
./install_dependencies.sh
./build_and_run.sh
```

### Option 2: Manual Setup
```bash
# Install nlohmann/json (Ubuntu/Debian)
sudo apt-get install nlohmann-json3-dev

# Build and run
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
./rrjson_vs_nlohmann
```

### Option 3: Simple Compilation
```bash
# If nlohmann/json is installed system-wide
g++ -std=c++23 -O3 -DHAS_NLOHMANN_JSON rrjson_vs_nlohmann.cpp -o benchmark
./benchmark
```

## 📋 Prerequisites

### Required
- **C++23 compatible compiler** (GCC 12+, Clang 15+)
- **CMake 3.16+** (for automated build)
- **rrjson library** (included in parent directory)

### Optional (for comparison)
- **nlohmann/json** - Primary comparison target
- **RapidJSON** - Additional fast parser
- **Boost.JSON** - Boost ecosystem integration
- **simdjson** - SIMD-optimized parser

## 🔧 Installation Guide

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install nlohmann-json3-dev rapidjson-dev libboost-json-dev libsimdjson-dev
```

### Fedora/RHEL/CentOS
```bash
sudo dnf install json-devel rapidjson-devel boost-devel
```

### macOS
```bash
brew install nlohmann-json rapidjson boost simdjson
```

### Arch Linux
```bash
sudo pacman -S nlohmann-json rapidjson boost simdjson
```

## 📊 Understanding the Results

### Performance Metrics Explained

| Metric | Description | Lower is Better |
|--------|-------------|-----------------|
| **Parse (ms)** | Time to parse JSON string into memory structure | ✅ |
| **Access (ms)** | Time to read values from parsed JSON | ✅ |
| **Modify (ms)** | Time to modify JSON data (if supported) | ✅ |
| **Serial (ms)** | Time to convert JSON back to string | ✅ |
| **Error (ms)** | Time to handle various error conditions | ✅ |
| **Memory (KB)** | Approximate memory usage | ✅ |

### Library Characteristics

#### rrjson
- ✅ **Zero-copy string access** using `string_view`
- ✅ **Minimal memory overhead** - stores only original JSON
- ✅ **Modern C++ design** with concepts and trailing return types
- ✅ **Fast error handling** with detailed exception types
- ❌ **Read-only** - no modification support
- ❌ **No serialization** - cannot generate new JSON

#### nlohmann/json
- ✅ **Full JSON manipulation** - create, modify, delete
- ✅ **Rich API** with STL-like interface
- ✅ **JSON serialization** with formatting options
- ✅ **Mature ecosystem** with extensive documentation
- ❌ **Higher memory usage** - stores parsed tree structure
- ❌ **String copying** - no zero-copy access

## 🎯 Use Case Recommendations

### Choose rrjson when:
- 📖 **Read-only JSON processing** is sufficient
- 🚀 **Performance is critical** for parsing and access
- 💾 **Memory efficiency** is important
- 🔧 **Modern C++ features** are preferred
- 📊 **Processing large JSON datasets** efficiently

### Choose nlohmann/json when:
- ✏️ **JSON modification** is required
- 📤 **JSON generation/serialization** is needed
- 🔄 **Existing codebase** already uses nlohmann/json
- 🛠️ **Rich feature set** is more important than raw speed
- 📚 **Mature, well-documented** library is preferred

## 🔬 Advanced Usage

### Running Specific Tests
```bash
# Build all benchmarks
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)

# Run focused comparison
./rrjson_vs_nlohmann

# Run comprehensive comparison (if available)
./json_benchmark

# Run traditional parsers only (no SIMD)
./json_benchmark_no_simd
```

### Custom Compilation Flags
```bash
# Maximum optimization
g++ -std=c++23 -O3 -march=native -DNDEBUG -DHAS_NLOHMANN_JSON \
    rrjson_vs_nlohmann.cpp -o benchmark

# Debug build with symbols
g++ -std=c++23 -g -O0 -DHAS_NLOHMANN_JSON \
    rrjson_vs_nlohmann.cpp -o benchmark_debug
```

### Profiling and Analysis
```bash
# Profile with perf (Linux)
perf record -g ./rrjson_vs_nlohmann
perf report

# Memory analysis with valgrind
valgrind --tool=massif ./rrjson_vs_nlohmann
```

## 📈 Interpreting Results

### Performance Patterns
- **Small datasets**: Overhead dominates, differences may be minimal
- **Medium datasets**: Clear performance characteristics emerge
- **Large datasets**: Scalability differences become apparent
- **Nested data**: Access pattern efficiency becomes important

### Statistical Considerations
- Run multiple times for statistical accuracy
- System load affects results
- Compiler optimizations impact performance
- Hardware architecture influences SIMD benefits

## 🐛 Troubleshooting

### Common Issues

#### "nlohmann/json not found"
```bash
# Install development headers
sudo apt-get install nlohmann-json3-dev

# Or use header-only version
wget https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp
```

#### "C++23 not supported"
```bash
# Update compiler
sudo apt-get install gcc-12 g++-12
export CXX=g++-12

# Or use C++20
sed -i 's/c++23/c++20/g' CMakeLists.txt
```

#### Build failures
```bash
# Clean build
rm -rf build
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_VERBOSE_MAKEFILE=ON
```

## 📝 Contributing

### Adding New Libraries
1. Add detection logic to `CMakeLists.txt`
2. Implement benchmark function in `rrjson_vs_nlohmann.cpp`
3. Add conditional compilation guards
4. Update documentation

### Improving Benchmarks
- Add more realistic test scenarios
- Implement memory profiling
- Add multi-threading tests
- Include streaming scenarios

## 📄 License

This benchmark suite is part of the rrjson project and follows the same license terms.
