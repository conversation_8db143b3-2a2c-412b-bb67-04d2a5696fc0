#!/bin/bash

# Build and Run rrjson vs n<PERSON>hmann/json Ben<PERSON>mark
# Automatically builds and executes the benchmark comparison

set -e

echo "🚀 rrjson vs nlohmann/json Benchmark Builder"
echo "============================================"
echo

# Check if we're in the right directory
if [[ ! -f "rrjson_vs_nlohmann.cpp" ]]; then
    echo "❌ Error: rrjson_vs_nlohmann.cpp not found"
    echo "Please run this script from the benchmarks directory"
    exit 1
fi

# Check if rrjson header exists
if [[ ! -f "../lib/rrjson.hpp" ]]; then
    echo "❌ Error: rrjson.hpp not found in ../lib/"
    echo "Please ensure you're in the benchmarks directory of the rrjson project"
    exit 1
fi

# Create build directory
echo "📁 Creating build directory..."
mkdir -p build
cd build

# Configure with CMake
echo "🔧 Configuring with CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
echo "🔨 Building benchmark..."
make -j$(nproc 2>/dev/null || echo 4)

echo
echo "✅ Build completed successfully!"
echo

# Check what was built
if [[ -f "rrjson_vs_nlohmann" ]]; then
    echo "🎯 Running rrjson vs nlohmann/json benchmark..."
    echo "=============================================="
    echo
    ./rrjson_vs_nlohmann
    echo
    echo "✅ Benchmark completed!"
else
    echo "❌ Error: rrjson_vs_nlohmann executable not found"
    echo "Build may have failed"
    exit 1
fi

# Check for other benchmarks
echo
echo "🔍 Other available benchmarks:"
if [[ -f "json_benchmark" ]]; then
    echo "  • json_benchmark (comprehensive comparison)"
    echo "    Run with: ./json_benchmark"
fi

if [[ -f "json_benchmark_no_simd" ]]; then
    echo "  • json_benchmark_no_simd (traditional parsers only)"
    echo "    Run with: ./json_benchmark_no_simd"
fi

echo
echo "📊 Benchmark Results Summary:"
echo "=============================="
echo "The benchmark above compared rrjson with nlohmann/json across:"
echo "  • Parsing performance"
echo "  • Data access speed"
echo "  • Modification capabilities"
echo "  • Serialization performance"
echo "  • Error handling efficiency"
echo "  • Memory usage"
echo
echo "💡 Key Takeaways:"
echo "  • rrjson excels at read-only JSON processing with minimal memory overhead"
echo "  • nlohmann/json provides full JSON manipulation capabilities"
echo "  • Choose based on your specific use case requirements"
echo
echo "🔧 To run again:"
echo "  cd build && ./rrjson_vs_nlohmann"
echo
echo "📈 For statistical accuracy, consider running multiple times"
echo "🎯 Results may vary based on system load and hardware"
