#!/bin/bash

# JSON Library Dependencies Installer
# Installs popular C++ JSON libraries for benchmarking

set -e

echo "🚀 Installing JSON Library Dependencies for Benchmarking"
echo "========================================================"
echo

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu
        echo "📦 Detected Debian/Ubuntu system"
        echo "Installing packages with apt-get..."
        
        sudo apt-get update
        
        # Install nlohmann/json
        echo "Installing nlohmann/json..."
        sudo apt-get install -y nlohmann-json3-dev
        
        # Install RapidJSON
        echo "Installing RapidJSON..."
        sudo apt-get install -y rapidjson-dev
        
        # Install Boost.JSON (if available)
        echo "Installing Boost.JSON..."
        sudo apt-get install -y libboost-json-dev || echo "⚠️  Boost.JSON not available in this version"
        
        # Install simdjson
        echo "Installing simdjson..."
        sudo apt-get install -y libsimdjson-dev || echo "⚠️  simdjson not available, will build from source"
        
        # Install build tools
        echo "Installing build tools..."
        sudo apt-get install -y build-essential cmake pkg-config
        
    elif command -v dnf &> /dev/null; then
        # Fedora/RHEL/CentOS
        echo "📦 Detected Fedora/RHEL/CentOS system"
        echo "Installing packages with dnf..."
        
        # Install nlohmann/json
        echo "Installing nlohmann/json..."
        sudo dnf install -y json-devel
        
        # Install RapidJSON
        echo "Installing RapidJSON..."
        sudo dnf install -y rapidjson-devel
        
        # Install Boost
        echo "Installing Boost..."
        sudo dnf install -y boost-devel
        
        # Install build tools
        echo "Installing build tools..."
        sudo dnf install -y gcc-c++ cmake pkgconfig
        
    elif command -v yum &> /dev/null; then
        # Older RHEL/CentOS
        echo "📦 Detected older RHEL/CentOS system"
        echo "Installing packages with yum..."
        
        # Install EPEL repository for additional packages
        sudo yum install -y epel-release
        
        # Install available packages
        echo "Installing available JSON libraries..."
        sudo yum install -y rapidjson-devel boost-devel
        
        # Install build tools
        echo "Installing build tools..."
        sudo yum install -y gcc-c++ cmake pkgconfig
        
        echo "⚠️  nlohmann/json may need to be installed manually"
        
    elif command -v pacman &> /dev/null; then
        # Arch Linux
        echo "📦 Detected Arch Linux system"
        echo "Installing packages with pacman..."
        
        sudo pacman -Syu
        
        # Install JSON libraries
        echo "Installing JSON libraries..."
        sudo pacman -S --noconfirm nlohmann-json rapidjson boost simdjson
        
        # Install build tools
        echo "Installing build tools..."
        sudo pacman -S --noconfirm base-devel cmake pkgconf
        
    else
        echo "❌ Unsupported Linux distribution"
        echo "Please install the following packages manually:"
        echo "  - nlohmann-json development headers"
        echo "  - rapidjson development headers"
        echo "  - boost development headers"
        echo "  - simdjson development headers"
        echo "  - cmake and build tools"
        exit 1
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "📦 Detected macOS system"
    
    if command -v brew &> /dev/null; then
        echo "Installing packages with Homebrew..."
        
        # Update Homebrew
        brew update
        
        # Install JSON libraries
        echo "Installing JSON libraries..."
        brew install nlohmann-json rapidjson boost simdjson
        
        # Install build tools
        echo "Installing build tools..."
        brew install cmake pkg-config
        
    else
        echo "❌ Homebrew not found"
        echo "Please install Homebrew first: https://brew.sh/"
        echo "Then run this script again"
        exit 1
    fi
    
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    # Windows with MSYS2/Cygwin
    echo "📦 Detected Windows with MSYS2/Cygwin"
    
    if command -v pacman &> /dev/null; then
        echo "Installing packages with pacman (MSYS2)..."
        
        # Update package database
        pacman -Syu
        
        # Install JSON libraries
        echo "Installing JSON libraries..."
        pacman -S --noconfirm mingw-w64-x86_64-nlohmann-json
        pacman -S --noconfirm mingw-w64-x86_64-rapidjson
        pacman -S --noconfirm mingw-w64-x86_64-boost
        
        # Install build tools
        echo "Installing build tools..."
        pacman -S --noconfirm mingw-w64-x86_64-gcc
        pacman -S --noconfirm mingw-w64-x86_64-cmake
        pacman -S --noconfirm mingw-w64-x86_64-pkg-config
        
    else
        echo "❌ MSYS2 pacman not found"
        echo "Please install packages manually or use vcpkg"
        exit 1
    fi
    
else
    echo "❌ Unsupported operating system: $OSTYPE"
    echo "Please install the following libraries manually:"
    echo "  - nlohmann/json"
    echo "  - RapidJSON"
    echo "  - Boost.JSON"
    echo "  - simdjson"
    echo "  - CMake and C++ build tools"
    exit 1
fi

echo
echo "✅ Installation completed!"
echo
echo "📋 Installed libraries:"
echo "  • nlohmann/json - Popular feature-rich JSON library"
echo "  • RapidJSON - Fast JSON parser and generator"
echo "  • Boost.JSON - Part of the Boost C++ libraries"
echo "  • simdjson - SIMD-accelerated JSON parser"
echo
echo "🔧 Next steps:"
echo "  1. cd benchmarks"
echo "  2. mkdir build && cd build"
echo "  3. cmake .. -DCMAKE_BUILD_TYPE=Release"
echo "  4. make -j$(nproc 2>/dev/null || echo 4)"
echo "  5. ./rrjson_vs_nlohmann"
echo
echo "🚀 Or use the quick build script:"
echo "  ./build_and_run.sh"
echo

# Verify installations
echo "🔍 Verifying installations..."
echo

# Check for nlohmann/json
if pkg-config --exists nlohmann_json 2>/dev/null; then
    echo "✅ nlohmann/json: $(pkg-config --modversion nlohmann_json)"
elif find /usr/include /usr/local/include /opt/homebrew/include -name "json.hpp" -path "*/nlohmann/*" 2>/dev/null | head -1 | grep -q .; then
    echo "✅ nlohmann/json: Found headers"
else
    echo "⚠️  nlohmann/json: Not found"
fi

# Check for RapidJSON
if find /usr/include /usr/local/include /opt/homebrew/include -name "rapidjson.h" -path "*/rapidjson/*" 2>/dev/null | head -1 | grep -q .; then
    echo "✅ RapidJSON: Found headers"
else
    echo "⚠️  RapidJSON: Not found"
fi

# Check for Boost
if pkg-config --exists boost 2>/dev/null; then
    echo "✅ Boost: $(pkg-config --modversion boost)"
elif find /usr/include /usr/local/include /opt/homebrew/include -name "json.hpp" -path "*/boost/*" 2>/dev/null | head -1 | grep -q .; then
    echo "✅ Boost: Found headers"
else
    echo "⚠️  Boost: Not found"
fi

# Check for simdjson
if find /usr/include /usr/local/include /opt/homebrew/include -name "simdjson.h" 2>/dev/null | head -1 | grep -q .; then
    echo "✅ simdjson: Found headers"
else
    echo "⚠️  simdjson: Not found"
fi

echo
echo "🎉 Setup complete! Ready to run benchmarks."
