#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <functional>
#include <cstring>

// Include all JSON libraries
#include "rrjson.hpp"

#ifdef HAS_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#endif

#ifdef HAS_RAPIDJSON
#include <rapidjson/document.h>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#endif

#ifdef HAS_SIMDJSON
#include <simdjson.h>
#endif

#ifdef HAS_BOOST_JSON
#include <boost/json.hpp>
#endif

/**
 * @brief Benchmark framework for JSON libraries
 */
class JsonBenchmark {
public:
    struct BenchmarkResult {
        std::string library_name;
        double parse_time_ms;
        double access_time_ms;
        double error_handling_time_ms;
        size_t memory_usage_bytes;
        bool success;
        std::string error_message;
    };

    struct TestData {
        std::string name;
        std::string json_content;
        size_t expected_array_size;
        std::string expected_version;
    };

private:
    std::vector<TestData> test_datasets_;
    static constexpr int ITERATIONS = 100;
    static constexpr int ACCESS_ITERATIONS = 1000;

public:
    JsonBenchmark() {
        generate_test_datasets();
    }

    auto run_all_benchmarks() -> void {
        std::cout << "JSON Library Benchmark Comparison\n";
        std::cout << "==================================\n\n";

        for (const auto& dataset : test_datasets_) {
            std::cout << "Testing with dataset: " << dataset.name << "\n";
            std::cout << "JSON size: " << dataset.json_content.size() << " bytes\n";
            std::cout << "Expected array size: " << dataset.expected_array_size << "\n\n";

            std::vector<BenchmarkResult> results;

            // Test rrjson
            results.push_back(benchmark_rrjson(dataset));

#ifdef HAS_NLOHMANN_JSON
            results.push_back(benchmark_nlohmann(dataset));
#endif

#ifdef HAS_RAPIDJSON
            results.push_back(benchmark_rapidjson(dataset));
#endif

// Exclude simdjson for non-SIMD comparison
#if defined(HAS_SIMDJSON) && !defined(NO_SIMD_BENCHMARKS)
            results.push_back(benchmark_simdjson(dataset));
#endif

#ifdef HAS_BOOST_JSON
            results.push_back(benchmark_boost_json(dataset));
#endif

            print_results(results);
            std::cout << "\n" << std::string(80, '-') << "\n\n";
        }
    }

private:
    auto generate_test_datasets() -> void {
        // Small dataset
        test_datasets_.push_back({
            "Small (100 objects)",
            generate_json_data(100),
            100,
            "1.0"
        });

        // Medium dataset
        test_datasets_.push_back({
            "Medium (1000 objects)",
            generate_json_data(1000),
            1000,
            "1.0"
        });

        // Large dataset
        test_datasets_.push_back({
            "Large (10000 objects)",
            generate_json_data(10000),
            10000,
            "1.0"
        });
    }

    auto generate_json_data(size_t num_objects) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"metadata\": {\n";
        oss << "    \"version\": \"1.0\",\n";
        oss << "    \"count\": " << num_objects << ",\n";
        oss << "    \"generated\": true,\n";
        oss << "    \"timestamp\": 1640995200,\n";
        oss << "    \"description\": \"Benchmark test data with " << num_objects << " objects\"\n";
        oss << "  },\n";
        oss << "  \"data\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            oss << "    {\n";
            oss << "      \"id\": " << i << ",\n";
            oss << "      \"name\": \"Object " << i << "\",\n";
            oss << "      \"value\": " << (i * 3.14159) << ",\n";
            oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << ",\n";
            oss << "      \"category\": \"category_" << (i % 10) << "\",\n";
            oss << "      \"tags\": [\"tag_" << i << "\", \"type_" << (i % 5) << "\"],\n";
            oss << "      \"nested\": {\n";
            oss << "        \"level\": " << (i % 3) << ",\n";
            oss << "        \"score\": " << (i * 0.1) << "\n";
            oss << "      }\n";
            oss << "    }";
        }
        
        oss << "\n  ],\n";
        oss << "  \"summary\": {\n";
        oss << "    \"total_objects\": " << num_objects << ",\n";
        oss << "    \"active_count\": " << ((num_objects + 1) / 2) << ",\n";
        oss << "    \"categories\": 10\n";
        oss << "  }\n";
        oss << "}";
        
        return oss.str();
    }

    auto measure_time(std::function<void()> func) -> double {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        return duration.count() / 1000000.0; // Convert to milliseconds
    }

#ifdef HAS_NLOHMANN_JSON
    auto benchmark_nlohmann(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "nlohmann/json";
        result.success = true;

        try {
            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    auto root = nlohmann::json::parse(json_copy);
                    // Force evaluation
                    volatile auto type = root.type();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            auto root = nlohmann::json::parse(data.json_content);

            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    auto metadata = root["metadata"];
                    auto version = metadata["version"].get<std::string>();
                    auto count = metadata["count"].get<int>();
                    auto data_array = root["data"];

                    size_t idx = i % std::min(data.expected_array_size, size_t(100));
                    auto obj = data_array[idx];
                    auto id = obj["id"].get<int>();
                    auto name = obj["name"].get<std::string>();
                    auto value = obj["value"].get<double>();
                    auto nested = obj["nested"];
                    auto level = nested["level"].get<int>();

                    volatile auto v1 = version.size();
                    volatile auto v2 = count + id;
                    volatile auto v3 = value + level;
                    (void)v1; (void)v2; (void)v3;
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Error handling benchmark
            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    try {
                        auto nonexistent = root["nonexistent_key"];
                    } catch (const std::exception&) {
                        // Expected
                    }

                    try {
                        auto out_of_bounds = root["data"][data.expected_array_size + 100];
                    } catch (const std::exception&) {
                        // Expected
                    }

                    try {
                        auto wrong_type = root["metadata"]["version"].get<double>();
                    } catch (const std::exception&) {
                        // Expected
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            result.memory_usage_bytes = data.json_content.size() + 2048; // Rough estimate

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }
#endif

#ifdef HAS_RAPIDJSON
    auto benchmark_rapidjson(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "RapidJSON";
        result.success = true;

        try {
            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    rapidjson::Document doc;
                    doc.Parse(json_copy.c_str());
                    if (doc.HasParseError()) {
                        throw std::runtime_error("Parse error");
                    }
                    volatile auto type = doc.GetType();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            rapidjson::Document doc;
            doc.Parse(data.json_content.c_str());

            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    auto& metadata = doc["metadata"];
                    auto version = metadata["version"].GetString();
                    auto count = metadata["count"].GetInt();
                    auto& data_array = doc["data"];

                    size_t idx = i % std::min(data.expected_array_size, size_t(100));
                    auto& obj = data_array[idx];
                    auto id = obj["id"].GetInt();
                    auto name = obj["name"].GetString();
                    auto value = obj["value"].GetDouble();
                    auto& nested = obj["nested"];
                    auto level = nested["level"].GetInt();

                    volatile auto v1 = strlen(version);
                    volatile auto v2 = count + id;
                    volatile auto v3 = value + level;
                    (void)v1; (void)v2; (void)v3;
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Error handling benchmark (RapidJSON doesn't throw, so we simulate)
            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    // Check for non-existent key
                    if (!doc.HasMember("nonexistent_key")) {
                        // Expected
                    }

                    // Check for out of bounds
                    auto& data_array = doc["data"];
                    if (data.expected_array_size + 100 >= data_array.Size()) {
                        // Expected
                    }

                    // Check for wrong type
                    auto& version = doc["metadata"]["version"];
                    if (!version.IsNumber()) {
                        // Expected
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            result.memory_usage_bytes = data.json_content.size() + 1536; // Rough estimate

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }
#endif

    auto benchmark_rrjson(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "rrjson";
        result.success = true;

        try {
            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    rrjson::Element root(std::move(json_copy));
                    // Force evaluation to ensure parsing is complete
                    volatile auto type = root.type();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            std::string json_copy = data.json_content;
            rrjson::Element root(std::move(json_copy));
            
            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    auto metadata = root["metadata"];
                    auto version = metadata["version"].as_string();
                    auto count = metadata["count"].as_int();
                    auto data_array = root["data"];
                    
                    // Access some random elements
                    size_t idx = i % std::min(data.expected_array_size, size_t(100));
                    auto obj = data_array[idx];
                    auto id = obj["id"].as_int();
                    auto name = obj["name"].as_string();
                    auto value = obj["value"].as_number();
                    auto nested = obj["nested"];
                    auto level = nested["level"].as_int();
                    
                    // Use values to prevent optimization
                    volatile auto v1 = version.size();
                    volatile auto v2 = count + id;
                    volatile auto v3 = value + level;
                    (void)v1; (void)v2; (void)v3;
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Error handling benchmark
            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    try {
                        auto nonexistent = root["nonexistent_key"];
                    } catch (const rrjson::key_error&) {
                        // Expected
                    }
                    
                    try {
                        auto out_of_bounds = root["data"][data.expected_array_size + 100];
                    } catch (const rrjson::index_error&) {
                        // Expected
                    }
                    
                    try {
                        auto wrong_type = root["metadata"]["version"].as_number();
                    } catch (const rrjson::type_error&) {
                        // Expected
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            // Rough memory usage estimate (JSON data size + overhead)
            result.memory_usage_bytes = data.json_content.size() + 1024; // Rough estimate

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }

#ifdef HAS_SIMDJSON
    auto benchmark_simdjson(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "simdjson";
        result.success = true;

        try {
            simdjson::dom::parser parser;

            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    auto doc = parser.parse(json_copy);
                    volatile auto type = doc.type();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            auto doc = parser.parse(data.json_content);

            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    auto metadata = doc["metadata"];
                    auto version = std::string_view(metadata["version"]);
                    auto count = int64_t(metadata["count"]);
                    auto data_array = doc["data"];

                    size_t idx = i % std::min(data.expected_array_size, size_t(100));
                    auto obj = data_array.at(idx);
                    auto id = int64_t(obj["id"]);
                    auto name = std::string_view(obj["name"]);
                    auto value = double(obj["value"]);
                    auto nested = obj["nested"];
                    auto level = int64_t(nested["level"]);

                    volatile auto v1 = version.size();
                    volatile auto v2 = count + id;
                    volatile auto v3 = value + level;
                    (void)v1; (void)v2; (void)v3;
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Error handling benchmark (simdjson uses error codes)
            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    // Check for non-existent key
                    auto nonexistent_result = doc["nonexistent_key"];
                    if (nonexistent_result.error() != simdjson::SUCCESS) {
                        // Expected
                    }

                    // Check for out of bounds
                    auto data_array = doc["data"];
                    auto out_of_bounds_result = data_array.at(data.expected_array_size + 100);
                    if (out_of_bounds_result.error() != simdjson::SUCCESS) {
                        // Expected
                    }

                    // Type checking is implicit in simdjson
                    auto version = doc["metadata"]["version"];
                    auto wrong_type_result = version.get<double>();
                    if (wrong_type_result.error() != simdjson::SUCCESS) {
                        // Expected
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            result.memory_usage_bytes = data.json_content.size() + 512; // Very efficient

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }
#endif

#ifdef HAS_BOOST_JSON
    auto benchmark_boost_json(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "Boost.JSON";
        result.success = true;

        try {
            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    auto root = boost::json::parse(json_copy);
                    volatile auto type = root.kind();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            auto root = boost::json::parse(data.json_content);

            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    auto& metadata = root.at("metadata");
                    auto version = metadata.at("version").as_string();
                    auto count = metadata.at("count").as_int64();
                    auto& data_array = root.at("data");

                    size_t idx = i % std::min(data.expected_array_size, size_t(100));
                    auto& obj = data_array.at(idx);
                    auto id = obj.at("id").as_int64();
                    auto name = obj.at("name").as_string();
                    auto value = obj.at("value").as_double();
                    auto& nested = obj.at("nested");
                    auto level = nested.at("level").as_int64();

                    volatile auto v1 = version.size();
                    volatile auto v2 = count + id;
                    volatile auto v3 = value + level;
                    (void)v1; (void)v2; (void)v3;
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Error handling benchmark
            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    try {
                        auto nonexistent = root.at("nonexistent_key");
                    } catch (const std::exception&) {
                        // Expected
                    }

                    try {
                        auto out_of_bounds = root.at("data").at(data.expected_array_size + 100);
                    } catch (const std::exception&) {
                        // Expected
                    }

                    try {
                        auto wrong_type = root.at("metadata").at("version").as_double();
                    } catch (const std::exception&) {
                        // Expected
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            result.memory_usage_bytes = data.json_content.size() + 1800; // Rough estimate

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }
#endif

    auto print_results(const std::vector<BenchmarkResult>& results) -> void {
        std::cout << std::left << std::setw(15) << "Library"
                  << std::setw(12) << "Parse (ms)"
                  << std::setw(12) << "Access (ms)"
                  << std::setw(12) << "Errors (ms)"
                  << std::setw(12) << "Memory (KB)"
                  << std::setw(8) << "Status" << "\n";
        std::cout << std::string(80, '-') << "\n";

        for (const auto& result : results) {
            std::cout << std::left << std::setw(15) << result.library_name;
            
            if (result.success) {
                std::cout << std::setw(12) << std::fixed << std::setprecision(3) << result.parse_time_ms
                          << std::setw(12) << std::fixed << std::setprecision(3) << result.access_time_ms
                          << std::setw(12) << std::fixed << std::setprecision(3) << result.error_handling_time_ms
                          << std::setw(12) << (result.memory_usage_bytes / 1024)
                          << std::setw(8) << "✓";
            } else {
                std::cout << std::setw(12) << "FAILED"
                          << std::setw(12) << "FAILED"
                          << std::setw(12) << "FAILED"
                          << std::setw(12) << "FAILED"
                          << std::setw(8) << "✗";
            }
            std::cout << "\n";
            
            if (!result.success) {
                std::cout << "  Error: " << result.error_message << "\n";
            }
        }
    }
};

int main() {
    JsonBenchmark benchmark;
    benchmark.run_all_benchmarks();
    return 0;
}
