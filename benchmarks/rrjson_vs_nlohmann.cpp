#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <functional>
#include <cstring>

// Include rrjson
#include "../lib/rrjson.hpp"

// Include nlohmann/json (conditional)
#ifdef HAS_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#endif

/**
 * @brief Focused benchmark comparing rrjson with nlohmann/json
 * 
 * This benchmark provides a detailed comparison between rrjson and nlohmann/json,
 * the most popular C++ JSON library, across various scenarios and data sizes.
 */
class RRJsonVsNlohmannBenchmark {
public:
    struct BenchmarkResult {
        std::string library_name;
        double parse_time_ms;
        double access_time_ms;
        double modification_time_ms;
        double serialization_time_ms;
        double error_handling_time_ms;
        size_t memory_usage_bytes;
        bool success;
        std::string error_message;
    };

    struct TestData {
        std::string name;
        std::string json_content;
        size_t expected_array_size;
        std::string expected_version;
    };

private:
    std::vector<TestData> test_datasets_;
    static constexpr int ITERATIONS = 100;
    static constexpr int ACCESS_ITERATIONS = 1000;
    static constexpr int MODIFICATION_ITERATIONS = 100;

public:
    RRJsonVsNlohmannBenchmark() {
        generate_test_datasets();
    }

    auto run_all_benchmarks() -> void {
        std::cout << "rrjson vs nlohmann/json Detailed Comparison\n";
        std::cout << "==========================================\n\n";
        std::cout << "This benchmark compares rrjson with nlohmann/json across multiple scenarios:\n";
        std::cout << "- Parsing performance\n";
        std::cout << "- Data access patterns\n";
        std::cout << "- Modification operations\n";
        std::cout << "- Serialization speed\n";
        std::cout << "- Error handling efficiency\n\n";

        for (const auto& dataset : test_datasets_) {
            std::cout << "Testing with dataset: " << dataset.name << "\n";
            std::cout << "JSON size: " << dataset.json_content.size() << " bytes\n";
            std::cout << "Expected array size: " << dataset.expected_array_size << "\n\n";

            std::vector<BenchmarkResult> results;

            // Test rrjson
            results.push_back(benchmark_rrjson(dataset));

#ifdef HAS_NLOHMANN_JSON
            results.push_back(benchmark_nlohmann(dataset));
#else
            std::cout << "⚠️  nlohmann/json not available. Install with:\n";
            std::cout << "   sudo apt-get install nlohmann-json3-dev\n";
            std::cout << "   or use the install_dependencies.sh script\n\n";
#endif

            print_results(results);
            print_detailed_analysis(results);
            std::cout << "\n" << std::string(80, '-') << "\n\n";
        }
    }

private:
    auto generate_test_datasets() -> void {
        // Small dataset - typical API response
        test_datasets_.push_back({
            "Small API Response (100 objects)",
            generate_api_response_json(100),
            100,
            "1.0"
        });

        // Medium dataset - data export
        test_datasets_.push_back({
            "Medium Data Export (1000 objects)",
            generate_complex_json(1000),
            1000,
            "2.0"
        });

        // Large dataset - bulk data processing
        test_datasets_.push_back({
            "Large Bulk Data (10000 objects)",
            generate_complex_json(10000),
            10000,
            "3.0"
        });

        // Nested structure - configuration file
        test_datasets_.push_back({
            "Deeply Nested Config (500 objects)",
            generate_nested_json(500, 5),
            500,
            "1.0"
        });
    }

    auto generate_api_response_json(size_t num_objects) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"status\": \"success\",\n";
        oss << "  \"timestamp\": \"2024-06-13T10:30:00Z\",\n";
        oss << "  \"total_count\": " << num_objects << ",\n";
        oss << "  \"page\": 1,\n";
        oss << "  \"per_page\": " << num_objects << ",\n";
        oss << "  \"data\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            oss << "    {\n";
            oss << "      \"id\": " << i << ",\n";
            oss << "      \"name\": \"User " << i << "\",\n";
            oss << "      \"email\": \"user" << i << "@example.com\",\n";
            oss << "      \"score\": " << (i * 1.5 + 10.0) << ",\n";
            oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << ",\n";
            oss << "      \"tags\": [\"tag" << i << "\", \"category" << (i % 5) << "\"],\n";
            oss << "      \"metadata\": {\n";
            oss << "        \"created_at\": \"2024-01-" << (i % 28 + 1) << "T00:00:00Z\",\n";
            oss << "        \"updated_at\": \"2024-06-" << (i % 13 + 1) << "T12:00:00Z\",\n";
            oss << "        \"version\": " << (i % 10 + 1) << "\n";
            oss << "      }\n";
            oss << "    }";
        }
        
        oss << "\n  ]\n";
        oss << "}";
        
        return oss.str();
    }

    auto generate_complex_json(size_t num_objects) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"metadata\": {\n";
        oss << "    \"version\": \"2.0\",\n";
        oss << "    \"generator\": \"rrjson-benchmark\",\n";
        oss << "    \"count\": " << num_objects << ",\n";
        oss << "    \"generated_at\": \"2024-06-13T10:30:00Z\",\n";
        oss << "    \"schema_version\": \"1.2.3\"\n";
        oss << "  },\n";
        oss << "  \"data\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            oss << "    {\n";
            oss << "      \"id\": " << i << ",\n";
            oss << "      \"name\": \"Object " << i << "\",\n";
            oss << "      \"value\": " << (i * 3.14159) << ",\n";
            oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << ",\n";
            oss << "      \"category\": \"type_" << (i % 10) << "\",\n";
            oss << "      \"tags\": [\"tag" << i << "\", \"category" << (i % 5) << "\", \"group" << (i % 3) << "\"],\n";
            oss << "      \"properties\": {\n";
            oss << "        \"priority\": " << (i % 5 + 1) << ",\n";
            oss << "        \"weight\": " << (i * 0.1 + 1.0) << ",\n";
            oss << "        \"enabled\": " << (i % 3 == 0 ? "true" : "false") << "\n";
            oss << "      },\n";
            oss << "      \"nested\": {\n";
            oss << "        \"level\": " << (i % 10) << ",\n";
            oss << "        \"depth\": " << (i % 5) << ",\n";
            oss << "        \"coordinates\": {\n";
            oss << "          \"x\": " << (i * 0.5) << ",\n";
            oss << "          \"y\": " << (i * 0.7) << ",\n";
            oss << "          \"z\": " << (i * 0.3) << "\n";
            oss << "        }\n";
            oss << "      }\n";
            oss << "    }";
        }
        
        oss << "\n  ],\n";
        oss << "  \"summary\": {\n";
        oss << "    \"total_objects\": " << num_objects << ",\n";
        oss << "    \"active_count\": " << ((num_objects + 1) / 2) << ",\n";
        oss << "    \"categories\": 10,\n";
        oss << "    \"average_value\": " << (num_objects * 3.14159 / 2) << "\n";
        oss << "  }\n";
        oss << "}";
        
        return oss.str();
    }

    auto generate_nested_json(size_t num_objects, int max_depth) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"config\": {\n";
        oss << "    \"version\": \"1.0\",\n";
        oss << "    \"max_depth\": " << max_depth << ",\n";
        oss << "    \"total_nodes\": " << num_objects << "\n";
        oss << "  },\n";
        oss << "  \"nodes\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            generate_nested_object(oss, i, max_depth, 2);
        }
        
        oss << "\n  ]\n";
        oss << "}";
        
        return oss.str();
    }

    auto generate_nested_object(std::ostringstream& oss, size_t id, int depth, int indent) -> void {
        std::string spaces(indent, ' ');
        oss << spaces << "{\n";
        oss << spaces << "  \"id\": " << id << ",\n";
        oss << spaces << "  \"depth\": " << depth << ",\n";
        oss << spaces << "  \"value\": " << (id * 1.618) << ",\n";
        
        if (depth > 0) {
            oss << spaces << "  \"children\": [\n";
            for (int i = 0; i < 2 && depth > 1; ++i) {
                if (i > 0) oss << ",\n";
                generate_nested_object(oss, id * 10 + i, depth - 1, indent + 4);
            }
            oss << "\n" << spaces << "  ]\n";
        } else {
            oss << spaces << "  \"leaf\": true\n";
        }
        
        oss << spaces << "}";
    }

    auto measure_time(std::function<void()> func) -> double {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        return duration.count() / 1000000.0; // Convert to milliseconds
    }

    // Helper function to check if a key exists in rrjson Element
    auto has_key(const rrjson::Element& element, std::string_view key) -> bool {
        try {
            auto test = element[key];
            return true;
        } catch (const rrjson::key_error&) {
            return false;
        }
    }

    auto benchmark_rrjson(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "rrjson";
        result.success = true;

        try {
            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    rrjson::Element root(std::move(json_copy));
                    // Force evaluation to ensure parsing is complete
                    volatile auto type = root.type();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            std::string json_copy = data.json_content;
            rrjson::Element root(std::move(json_copy));

            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    if (has_key(root, "metadata") || has_key(root, "config")) {
                        auto metadata = has_key(root, "metadata") ? root["metadata"] : root["config"];
                        auto version = metadata["version"].as_string();

                        if (has_key(root, "data")) {
                            auto data_array = root["data"];
                            if (data_array.size() > 0) {
                                size_t idx = i % std::min(data.expected_array_size, size_t(100));
                                auto obj = data_array[idx];
                                auto id = obj["id"].as_int();
                                auto name = obj["name"].as_string();

                                // Use values to prevent optimization
                                volatile auto v1 = version.size();
                                volatile auto v2 = id;
                                volatile auto v3 = name.size();
                                (void)v1; (void)v2; (void)v3;
                            }
                        } else if (has_key(root, "nodes")) {
                            auto nodes_array = root["nodes"];
                            if (nodes_array.size() > 0) {
                                size_t idx = i % std::min(data.expected_array_size, size_t(100));
                                auto obj = nodes_array[idx];
                                auto id = obj["id"].as_int();
                                auto depth = obj["depth"].as_int();

                                volatile auto v1 = version.size();
                                volatile auto v2 = id + depth;
                                (void)v1; (void)v2;
                            }
                        }
                    }
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Modification benchmark (rrjson is read-only, so we measure copy cost)
            std::vector<double> modification_times;
            for (int i = 0; i < MODIFICATION_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    // Simulate modification by creating new JSON string
                    std::string new_json = data.json_content;
                    // Simple modification simulation
                    size_t pos = new_json.find("\"version\"");
                    if (pos != std::string::npos) {
                        // This simulates the cost of modification in read-only library
                        volatile auto len = new_json.size();
                        (void)len;
                    }
                });
                modification_times.push_back(time);
            }
            result.modification_time_ms = std::accumulate(modification_times.begin(), modification_times.end(), 0.0) / modification_times.size();

            // Serialization benchmark (rrjson doesn't serialize, so measure string copy)
            std::vector<double> serialization_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    // rrjson doesn't have serialization, so we measure the cost of the original string
                    std::string serialized = data.json_content;
                    volatile auto len = serialized.size();
                    (void)len;
                });
                serialization_times.push_back(time);
            }
            result.serialization_time_ms = std::accumulate(serialization_times.begin(), serialization_times.end(), 0.0) / serialization_times.size();

            // Error handling benchmark
            std::string json_copy2 = data.json_content;
            rrjson::Element root2(std::move(json_copy2));

            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    try {
                        // Test various error scenarios
                        auto nonexistent = root2["nonexistent_key_" + std::to_string(i)];
                        (void)nonexistent;
                    } catch (const rrjson::key_error&) {
                        // Expected error
                    }

                    try {
                        if (has_key(root2, "data") && root2["data"].size() > 0) {
                            auto out_of_bounds = root2["data"][data.expected_array_size + i];
                            (void)out_of_bounds;
                        }
                    } catch (const rrjson::index_error&) {
                        // Expected error
                    }

                    try {
                        if (has_key(root2, "metadata") || has_key(root2, "config")) {
                            auto metadata = has_key(root2, "metadata") ? root2["metadata"] : root2["config"];
                            auto wrong_type = metadata["version"].as_number(); // String as number
                            (void)wrong_type;
                        }
                    } catch (const rrjson::type_error&) {
                        // Expected error
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            // Memory usage estimate (JSON data size + minimal overhead for rrjson)
            result.memory_usage_bytes = data.json_content.size() + 512; // Minimal overhead estimate

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }

#ifdef HAS_NLOHMANN_JSON
    auto benchmark_nlohmann(const TestData& data) -> BenchmarkResult {
        BenchmarkResult result;
        result.library_name = "nlohmann/json";
        result.success = true;

        try {
            // Parsing benchmark
            std::vector<double> parse_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                std::string json_copy = data.json_content;
                auto time = measure_time([&]() {
                    nlohmann::json root = nlohmann::json::parse(json_copy);
                    // Force evaluation
                    volatile auto type = root.type();
                    (void)type;
                });
                parse_times.push_back(time);
            }
            result.parse_time_ms = std::accumulate(parse_times.begin(), parse_times.end(), 0.0) / parse_times.size();

            // Access benchmark
            nlohmann::json root_access = nlohmann::json::parse(data.json_content);

            std::vector<double> access_times;
            for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    if (root_access.contains("metadata") || root_access.contains("config")) {
                        auto& metadata = root_access.contains("metadata") ? root_access["metadata"] : root_access["config"];
                        auto version = metadata["version"].get<std::string>();

                        if (root_access.contains("data")) {
                            auto& data_array = root_access["data"];
                            if (!data_array.empty()) {
                                size_t idx = i % std::min(data.expected_array_size, size_t(100));
                                auto& obj = data_array[idx];
                                auto id = obj["id"].get<int>();
                                auto name = obj["name"].get<std::string>();

                                // Use values to prevent optimization
                                volatile auto v1 = version.size();
                                volatile auto v2 = id;
                                volatile auto v3 = name.size();
                                (void)v1; (void)v2; (void)v3;
                            }
                        } else if (root_access.contains("nodes")) {
                            auto& nodes_array = root_access["nodes"];
                            if (!nodes_array.empty()) {
                                size_t idx = i % std::min(data.expected_array_size, size_t(100));
                                auto& obj = nodes_array[idx];
                                auto id = obj["id"].get<int>();
                                auto depth = obj["depth"].get<int>();

                                volatile auto v1 = version.size();
                                volatile auto v2 = id + depth;
                                (void)v1; (void)v2;
                            }
                        }
                    }
                });
                access_times.push_back(time);
            }
            result.access_time_ms = std::accumulate(access_times.begin(), access_times.end(), 0.0) / access_times.size();

            // Modification benchmark
            std::vector<double> modification_times;
            for (int i = 0; i < MODIFICATION_ITERATIONS; ++i) {
                nlohmann::json root = nlohmann::json::parse(data.json_content);

                auto time = measure_time([&]() {
                    // Perform actual modifications
                    if (root.contains("metadata")) {
                        root["metadata"]["modified"] = true;
                        root["metadata"]["modification_count"] = i;
                    } else if (root.contains("config")) {
                        root["config"]["modified"] = true;
                        root["config"]["modification_count"] = i;
                    }

                    if (root.contains("data") && !root["data"].empty()) {
                        root["data"][0]["modified"] = true;
                    } else if (root.contains("nodes") && !root["nodes"].empty()) {
                        root["nodes"][0]["modified"] = true;
                    }
                });
                modification_times.push_back(time);
            }
            result.modification_time_ms = std::accumulate(modification_times.begin(), modification_times.end(), 0.0) / modification_times.size();

            // Serialization benchmark
            nlohmann::json root_serial = nlohmann::json::parse(data.json_content);
            std::vector<double> serialization_times;
            for (int i = 0; i < ITERATIONS; ++i) {
                auto time = measure_time([&]() {
                    std::string serialized = root_serial.dump();
                    volatile auto len = serialized.size();
                    (void)len;
                });
                serialization_times.push_back(time);
            }
            result.serialization_time_ms = std::accumulate(serialization_times.begin(), serialization_times.end(), 0.0) / serialization_times.size();

            // Error handling benchmark
            nlohmann::json root_error = nlohmann::json::parse(data.json_content);
            std::vector<double> error_times;
            for (int i = 0; i < 100; ++i) {
                auto time = measure_time([&]() {
                    try {
                        // Test various error scenarios
                        auto nonexistent = root_error["nonexistent_key_" + std::to_string(i)];
                        (void)nonexistent;
                    } catch (const nlohmann::json::exception&) {
                        // Expected error
                    }

                    try {
                        if (root_error.contains("data") && !root_error["data"].empty()) {
                            auto out_of_bounds = root_error["data"][data.expected_array_size + i];
                            (void)out_of_bounds;
                        }
                    } catch (const nlohmann::json::exception&) {
                        // Expected error
                    }

                    try {
                        if (root_error.contains("metadata") || root_error.contains("config")) {
                            auto& metadata = root_error.contains("metadata") ? root_error["metadata"] : root_error["config"];
                            auto wrong_type = metadata["version"].template get<double>(); // String as number
                            (void)wrong_type;
                        }
                    } catch (const nlohmann::json::exception&) {
                        // Expected error
                    }
                });
                error_times.push_back(time);
            }
            result.error_handling_time_ms = std::accumulate(error_times.begin(), error_times.end(), 0.0) / error_times.size();

            // Memory usage estimate (rough approximation)
            result.memory_usage_bytes = data.json_content.size() * 2; // nlohmann typically uses more memory

        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
        }

        return result;
    }
#endif

    auto print_results(const std::vector<BenchmarkResult>& results) -> void {
        std::cout << std::fixed << std::setprecision(3);

        std::cout << "Performance Results:\n";
        std::cout << "===================\n\n";

        // Header
        std::cout << std::left << std::setw(15) << "Library"
                  << std::setw(12) << "Parse (ms)"
                  << std::setw(12) << "Access (ms)"
                  << std::setw(12) << "Modify (ms)"
                  << std::setw(12) << "Serial (ms)"
                  << std::setw(12) << "Error (ms)"
                  << std::setw(12) << "Memory (KB)" << "\n";
        std::cout << std::string(90, '-') << "\n";

        for (const auto& result : results) {
            if (result.success) {
                std::cout << std::left << std::setw(15) << result.library_name
                          << std::setw(12) << result.parse_time_ms
                          << std::setw(12) << result.access_time_ms
                          << std::setw(12) << result.modification_time_ms
                          << std::setw(12) << result.serialization_time_ms
                          << std::setw(12) << result.error_handling_time_ms
                          << std::setw(12) << (result.memory_usage_bytes / 1024.0) << "\n";
            } else {
                std::cout << std::left << std::setw(15) << result.library_name
                          << "FAILED: " << result.error_message << "\n";
            }
        }
        std::cout << "\n";
    }

    auto print_detailed_analysis(const std::vector<BenchmarkResult>& results) -> void {
        if (results.size() < 2) {
            std::cout << "⚠️  Need at least 2 libraries for comparison\n";
            return;
        }

        auto rrjson_result = std::find_if(results.begin(), results.end(),
            [](const BenchmarkResult& r) { return r.library_name == "rrjson" && r.success; });
        auto nlohmann_result = std::find_if(results.begin(), results.end(),
            [](const BenchmarkResult& r) { return r.library_name == "nlohmann/json" && r.success; });

        if (rrjson_result == results.end() || nlohmann_result == results.end()) {
            std::cout << "⚠️  Cannot compare - one or both libraries failed\n";
            return;
        }

        std::cout << "Detailed Comparison Analysis:\n";
        std::cout << "============================\n\n";

        // Parse time comparison
        double parse_ratio = nlohmann_result->parse_time_ms / rrjson_result->parse_time_ms;
        std::cout << "📊 Parsing Performance:\n";
        if (parse_ratio > 1.1) {
            std::cout << "   ✅ rrjson is " << std::fixed << std::setprecision(1)
                      << parse_ratio << "x faster at parsing\n";
        } else if (parse_ratio < 0.9) {
            std::cout << "   ⚡ nlohmann/json is " << std::fixed << std::setprecision(1)
                      << (1.0 / parse_ratio) << "x faster at parsing\n";
        } else {
            std::cout << "   ⚖️  Similar parsing performance\n";
        }

        // Access time comparison
        double access_ratio = nlohmann_result->access_time_ms / rrjson_result->access_time_ms;
        std::cout << "\n🔍 Access Performance:\n";
        if (access_ratio > 1.1) {
            std::cout << "   ✅ rrjson is " << std::fixed << std::setprecision(1)
                      << access_ratio << "x faster at data access\n";
        } else if (access_ratio < 0.9) {
            std::cout << "   ⚡ nlohmann/json is " << std::fixed << std::setprecision(1)
                      << (1.0 / access_ratio) << "x faster at data access\n";
        } else {
            std::cout << "   ⚖️  Similar access performance\n";
        }

        // Modification comparison
        std::cout << "\n✏️  Modification Performance:\n";
        std::cout << "   📝 nlohmann/json: " << std::fixed << std::setprecision(3)
                  << nlohmann_result->modification_time_ms << " ms (native support)\n";
        std::cout << "   📖 rrjson: " << std::fixed << std::setprecision(3)
                  << rrjson_result->modification_time_ms << " ms (read-only, simulated cost)\n";
        std::cout << "   💡 nlohmann/json has native modification support\n";

        // Serialization comparison
        std::cout << "\n📤 Serialization Performance:\n";
        std::cout << "   📝 nlohmann/json: " << std::fixed << std::setprecision(3)
                  << nlohmann_result->serialization_time_ms << " ms (native JSON output)\n";
        std::cout << "   📖 rrjson: " << std::fixed << std::setprecision(3)
                  << rrjson_result->serialization_time_ms << " ms (original string copy)\n";
        std::cout << "   💡 nlohmann/json can generate formatted JSON output\n";

        // Memory usage comparison
        double memory_ratio = (double)nlohmann_result->memory_usage_bytes / rrjson_result->memory_usage_bytes;
        std::cout << "\n💾 Memory Usage:\n";
        std::cout << "   📖 rrjson: " << (rrjson_result->memory_usage_bytes / 1024.0) << " KB (minimal overhead)\n";
        std::cout << "   📝 nlohmann/json: " << (nlohmann_result->memory_usage_bytes / 1024.0) << " KB\n";
        if (memory_ratio > 1.2) {
            std::cout << "   ✅ rrjson uses " << std::fixed << std::setprecision(1)
                      << memory_ratio << "x less memory\n";
        } else {
            std::cout << "   ⚖️  Similar memory usage\n";
        }

        // Error handling comparison
        double error_ratio = nlohmann_result->error_handling_time_ms / rrjson_result->error_handling_time_ms;
        std::cout << "\n🚨 Error Handling:\n";
        if (error_ratio > 1.1) {
            std::cout << "   ✅ rrjson has " << std::fixed << std::setprecision(1)
                      << error_ratio << "x faster error handling\n";
        } else if (error_ratio < 0.9) {
            std::cout << "   ⚡ nlohmann/json has " << std::fixed << std::setprecision(1)
                      << (1.0 / error_ratio) << "x faster error handling\n";
        } else {
            std::cout << "   ⚖️  Similar error handling performance\n";
        }

        // Summary recommendations
        std::cout << "\n🎯 Use Case Recommendations:\n";
        std::cout << "   📖 Choose rrjson when:\n";
        std::cout << "      • You need fast read-only JSON processing\n";
        std::cout << "      • Memory efficiency is important\n";
        std::cout << "      • You want zero-copy string access\n";
        std::cout << "      • Modern C++ features are preferred\n\n";
        std::cout << "   📝 Choose nlohmann/json when:\n";
        std::cout << "      • You need to modify JSON data\n";
        std::cout << "      • JSON serialization is required\n";
        std::cout << "      • You want a mature, feature-rich library\n";
        std::cout << "      • Compatibility with existing code is important\n";
    }
};

int main() {
    std::cout << "🚀 Starting rrjson vs nlohmann/json Benchmark...\n\n";

    RRJsonVsNlohmannBenchmark benchmark;
    benchmark.run_all_benchmarks();

    std::cout << "\n✅ Benchmark completed successfully!\n\n";

    std::cout << "📚 Libraries Compared:\n";
    std::cout << "   • rrjson: Modern C++ read-only JSON library with zero-copy access\n";
    std::cout << "   • nlohmann/json: Popular feature-rich JSON library with modification support\n\n";

#ifndef HAS_NLOHMANN_JSON
    std::cout << "⚠️  To enable nlohmann/json comparison:\n";
    std::cout << "   Ubuntu/Debian: sudo apt-get install nlohmann-json3-dev\n";
    std::cout << "   Fedora/RHEL:   sudo dnf install json-devel\n";
    std::cout << "   macOS:         brew install nlohmann-json\n";
    std::cout << "   Or use:        ./install_dependencies.sh\n\n";
#endif

    std::cout << "🔧 Build with:\n";
    std::cout << "   g++ -std=c++23 -O3 -DHAS_NLOHMANN_JSON rrjson_vs_nlohmann.cpp -o benchmark\n\n";

    std::cout << "📊 For more detailed analysis, see the generated results above.\n";
    std::cout << "💡 Consider running multiple times for statistical accuracy.\n";

    return 0;
}
